package cn.ijiami.detection.android.client.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ClientUpdateProgressDTO.java
 * @Description 客户端更新进度DTO
 * @createTime 2025年01月15日 10:30:00
 */
@Data
@ApiModel(value = "客户端更新进度DTO")
@JsonIgnoreProperties(ignoreUnknown = true)
public class ClientUpdateProgressDTO implements Serializable {
    private static final long serialVersionUID = -127954056745752843L;

    @ApiModelProperty(value = "任务id")
    private Long taskId;

    @ApiModelProperty(value = "进度百分比")
    private Double progress;
}
