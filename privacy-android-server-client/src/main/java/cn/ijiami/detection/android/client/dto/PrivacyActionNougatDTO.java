package cn.ijiami.detection.android.client.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName PrivacyActionNougatDTO.java
 * @Description 行为数据DTO
 * @createTime 2025年01月15日 10:30:00
 */
@Data
@ApiModel(value = "行为数据DTO")
public class PrivacyActionNougatDTO implements Serializable {

    private static final long serialVersionUID = 3087019614631496622L;

    @ApiModelProperty(value = "ID")
    private Long id;

    @ApiModelProperty(value = "任务id")
    private Long taskId;

    @ApiModelProperty(value = "行为id")
    private Long actionId;

    @ApiModelProperty(value = "函数调用栈")
    private String stackInfo;

    @ApiModelProperty(value = "详细数据")
    private String detailsData;

    @ApiModelProperty(value = "行为触发时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date actionTime;

    @ApiModelProperty(value = "行为触发时间戳")
    private Long actionTimeStamp;

    @ApiModelProperty(value = "类型 是否是使用APP前触发")
    private Boolean type;

    @ApiModelProperty(value = "行为阶段")
    private Integer behaviorStage;

    @ApiModelProperty(value = "主体类型 1.APP 2.SDK")
    private Integer executorType = 2;

    @ApiModelProperty(value = "主体")
    private String executor;

    @ApiModelProperty(value = "主体包名")
    private String packageName;

    @ApiModelProperty(value = "调用次数")
    private Integer counter;

    @ApiModelProperty(value = "行为名称")
    private String actionName;

    @ApiModelProperty(value = "行为权限")
    private String actionPermission;

    @ApiModelProperty(value = "行为权限别名")
    private String actionPermissionAlias;

    @ApiModelProperty(value = "是否敏感")
    private Boolean sensitive;

    @ApiModelProperty(value = "详细信息")
    private List<PrivacyActionNougatDTO> privacyActionNougats;

    @ApiModelProperty(value = "APP调用次数")
    private Integer appCounter;

    @ApiModelProperty(value = "SDK调用次数")
    private Integer sdkCounter;

    @ApiModelProperty(value = "APP是否敏感")
    private Boolean appSensitive;

    @ApiModelProperty(value = "SDK是否敏感")
    private Boolean sdkSensitive;

    @ApiModelProperty(value = "主体为2存储sdkId，json格式")
    private String sdkIds;

    @ApiModelProperty(value = "是否个人信息相关")
    private String isPersonal;

    @ApiModelProperty(value = "触发周期次数")
    private Integer numberAction;

    @ApiModelProperty(value = "触发周期")
    private Long triggerCycleTime;

    @ApiModelProperty(value = "触发周期（秒/次）")
    private String triggerCycle;

    @ApiModelProperty(value = "主体类型（String类型）")
    private String executorTypeString;

    @ApiModelProperty(value = "行为截图集")
    private String screenshots;

    @ApiModelProperty(value = "备注")
    private String remarks;

    @ApiModelProperty(value = "jni函数调用栈")
    private String jniStackInfo;

    @ApiModelProperty(value = "触发行为的api名")
    private String apiName;
}
