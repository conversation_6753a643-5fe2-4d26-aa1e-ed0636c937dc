package cn.ijiami.detection.android.client.api;

import cn.ijiami.detection.DTO.TaskChangeStatusDTO;
import cn.ijiami.detection.VO.BigDataVO;
import cn.ijiami.detection.VO.ChongqinVO;
import cn.ijiami.detection.VO.ClientUpdateProgressVO;
import cn.ijiami.detection.VO.TaskDetailVO;
import cn.ijiami.detection.VO.TaskVO;
import cn.ijiami.detection.VO.detection.BaseMessageVO;
import cn.ijiami.detection.VO.detection.SdkResponseVO;
import cn.ijiami.detection.VO.detection.SdkVO;
import cn.ijiami.detection.entity.TPrivacyActionNougat;
import cn.ijiami.detection.entity.TTask;
import cn.ijiami.detection.query.task.TaskProgressQuery;
import cn.ijiami.detection.query.task.TaskQuery;
import cn.ijiami.detection.android.client.param.StartTaskParam;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectionTaskServiceApi.java
 * @Description 检测任务服务API接口
 * @createTime 2025年05月15日 10:00:00
 */
@Api("检测任务")
@FeignClient(value = "${ijiami-cloud-privacy-android-server-name:privacy-android-server}")
public interface DetectionTaskServiceApi {

    @ApiOperation("启动任务接口")
    @PostMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/task/startTask"},
            consumes = {"application/json"},
            produces = {"application/json"}
    )
    Long startTask(@Valid @RequestBody StartTaskParam param);

    @ApiOperation("停止任务")
    @PostMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/task/stopTask"},
            produces = {"application/json"}
    )
    Boolean stopTask(@RequestParam("taskId") Long taskId);

    @ApiOperation("强制停止任务")
    @PostMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/task/stopTaskForce"},
            produces = {"application/json"}
    )
    Boolean stopTask(@RequestParam("taskId") Long taskId, @RequestParam("forceStop") boolean forceStop);

    @ApiOperation("重启静态检测")
    @PostMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/task/restartTask"},
            produces = {"application/json"}
    )
    Long restartTask(@RequestParam("taskId") Long taskId);

    @ApiOperation("重启动态检测")
    @PostMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/task/restartDynamicTask"},
            produces = {"application/json"}
    )
    Long restartDynamicTask(@RequestParam("taskId") Long taskId);

    @ApiOperation("重启动态检测阶段")
    @PostMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/task/restartDynamicTaskStage"},
            produces = {"application/json"}
    )
    Integer restartDynamicTaskStage(@RequestParam("taskId") Long taskId);

    @ApiOperation("启动动态检测")
    @PostMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/task/startDynamicTask"},
            produces = {"application/json"}
    )
    Long startDynamicTask(@RequestParam("taskId") Long taskId, @RequestParam("type") Integer type);

    @ApiOperation("启动动态检测（带设备类型）")
    @PostMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/task/startDynamicTaskWithDevice"},
            produces = {"application/json"}
    )
    Long startDynamicTask(@RequestParam("taskId") Long taskId, @RequestParam("type") Integer type, @RequestParam("deviceType") int deviceType);

    @ApiOperation("开始AI智能检测")
    @PostMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/task/startAiDetection"},
            produces = {"application/json"}
    )
    Long startAiDetection(@RequestParam("taskId") Long taskId);

    @ApiOperation("任务抢占")
    @PostMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/task/taskPreempted"},
            produces = {"application/json"}
    )
    Long taskPreempted(@RequestParam("taskId") Long taskId, @RequestParam("deviceType") int deviceType);

    @ApiOperation("分页查询任务")
    @PostMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/task/findTaskByPage"},
            consumes = {"application/json"},
            produces = {"application/json"}
    )
    TaskVO findTaskByPage(@Valid @RequestBody TaskQuery taskQuery);

    @ApiOperation("查找已检测任务")
    @GetMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/task/findDetectedTasks"},
            produces = {"application/json"}
    )
    List<TTask> findDetectedTasks(@RequestParam("userId") Long userId);

    @ApiOperation("根据文档ID查找任务")
    @GetMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/task/findByDocumentId"},
            produces = {"application/json"}
    )
    TTask findByDocumentId(@RequestParam("documentId") String documentId);

    @ApiOperation("根据ID查找任务")
    @GetMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/task/findById"},
            produces = {"application/json"}
    )
    TTask findById(@RequestParam("taskId") Long taskId);

    @ApiOperation("获取任务详情")
    @GetMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/task/getTaskDetail"},
            produces = {"application/json"}
    )
    TaskDetailVO getTaskDetail(@RequestParam("id") String id);

    @ApiOperation("根据任务ID获取详情")
    @GetMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/task/getTaskDetailByTaskId"},
            produces = {"application/json"}
    )
    TaskDetailVO getTaskDetailByTaskId(@RequestParam("taskId") Long taskId);

    @ApiOperation("获取基础信息")
    @GetMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/task/getBaseMessage"},
            produces = {"application/json"}
    )
    BaseMessageVO getBaseMessage(@RequestParam("documentId") String documentId);

    @ApiOperation("获取SDK列表")
    @GetMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/task/getSdkList"},
            produces = {"application/json"}
    )
    List<SdkVO> getSdkList(@RequestParam("documentId") String documentId, @RequestParam("taskId") Long taskId);

    @ApiOperation("获取iOS SDK列表")
    @GetMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/task/getIosSDKList"},
            produces = {"application/json"}
    )
    SdkResponseVO getIosSDKList(@RequestParam("documentId") String documentId, @RequestParam("taskId") Long taskId);

    @ApiOperation("获取API SDK列表")
    @GetMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/task/getAPISdkList"},
            produces = {"application/json"}
    )
    List<SdkVO> getAPISdkList(@RequestParam("documentId") String documentId, @RequestParam("taskId") Long taskId);

    @ApiOperation("获取可疑SDK列表")
    @GetMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/task/getSuspiciousSdkList"},
            produces = {"application/json"}
    )
    List<SdkVO> getSuspiciousSdkList(@RequestParam("taskId") Long taskId);

    @ApiOperation("获取热更新SDK")
    @GetMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/task/getHotfixSdk"},
            produces = {"application/json"}
    )
    List<String> getHotfixSdk(@RequestParam("taskId") Long taskId);

    @ApiOperation("删除任务")
    @PostMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/task/deleteByTaskId"},
            produces = {"application/json"}
    )
    void deleteByTaskId(@RequestParam("taskId") Long taskId, @RequestParam("userId") Long userId, @RequestParam("isAdmin") boolean isAdmin);

    @ApiOperation("批量删除任务")
    @PostMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/task/deleteInTaskId"},
            consumes = {"application/json"},
            produces = {"application/json"}
    )
    void deleteInTaskId(@RequestBody List<Long> taskId, @RequestParam("userId") Long userId, @RequestParam("isAdmin") boolean isAdmin);

    @ApiOperation("修改任务状态")
    @PostMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/task/changeTaskStatus"},
            consumes = {"application/json"},
            produces = {"application/json"}
    )
    void changeTaskStatus(@Valid @RequestBody TaskChangeStatusDTO taskChangeStatusDTO);

    @ApiOperation("更新进度")
    @PostMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/task/updateProgress"},
            consumes = {"application/json"},
            produces = {"application/json"}
    )
    void updateProgress(@Valid @RequestBody ClientUpdateProgressVO clientUpdateProgressVO);

    @ApiOperation("刷新任务排序")
    @PostMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/task/refreshTaskSort"},
            consumes = {"application/json"},
            produces = {"application/json"}
    )
    void refreshTaskSort(@Valid @RequestBody TTask task);

    @ApiOperation("根据时间查询任务")
    @GetMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/task/findDetectedTasksByTime"},
            produces = {"application/json"}
    )
    List<String> findDetectedTasksByTime(@RequestParam("beginTime") String beginTime, @RequestParam("endTime") String endTime);

    @ApiOperation("启动iOS动态检测")
    @PostMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/task/startIosDynamicDetect"},
            consumes = {"application/json"},
            produces = {"application/json"}
    )
    void startIosDynamicDetect(@Valid @RequestBody TaskProgressQuery taskProgressQuery);

    @ApiOperation("停止iOS动态检测")
    @PostMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/task/stopIosDynamicDetect"},
            consumes = {"application/json"},
            produces = {"application/json"}
    )
    Boolean stopIosDynamicDetect(@Valid @RequestBody TaskProgressQuery taskProgressQuery);

    @ApiOperation("查找MD5")
    @GetMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/task/findMd5"},
            produces = {"application/json"}
    )
    List<BigDataVO> findMd5();

    @ApiOperation("根据MD5查找文档ID")
    @GetMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/task/findDocumentId"},
            produces = {"application/json"}
    )
    TTask findDocumentId(@RequestParam("md5") String md5);

    @ApiOperation("大数据回调")
    @PostMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/task/bigDataCallback"},
            consumes = {"application/json"},
            produces = {"application/json"}
    )
    void bigDataCallback(@Valid @RequestBody BigDataVO bigDataVO);

    @ApiOperation("重庆回调")
    @PostMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/task/chongqinCallback"},
            consumes = {"application/json"},
            produces = {"application/json"}
    )
    void chongqinCallback(@Valid @RequestBody ChongqinVO chongqinVO);

    @ApiOperation("删除个人词汇")
    @PostMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/task/delPersonalWord"},
            produces = {"application/json"}
    )
    void delPersonalWord(@RequestParam("type") Integer type, @RequestParam("id") Long id);

    @ApiOperation("IDB回调停止动态检测")
    @PostMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/task/stopDynamicByIdbCallback"},
            produces = {"application/json"}
    )
    void stopDynamicByIdbCallback(@RequestParam("taskId") Long taskId, @RequestParam("deviceId") String deviceId,
                                  @RequestParam("reason") String reason, @RequestParam("frpcPort") String frpcPort);

    @ApiOperation("根据MD5查找完成的检测")
    @GetMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/task/findDetectionCompleteByMd5"},
            produces = {"application/json"}
    )
    TTask findDetectionCompleteByMd5(@RequestParam("md5") String md5);

    @ApiOperation("更新SDK权限")
    @PostMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/task/updateSdkPermission"},
            consumes = {"application/json"},
            produces = {"application/json"}
    )
    void updateSdkPermission(@Valid @RequestBody List<TPrivacyActionNougat> nougatList);

    @ApiOperation("查找小程序动态检测数量")
    @GetMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/task/findAppletDynamicDetectionCount"},
            produces = {"application/json"}
    )
    Integer findAppletDynamicDetectionCount(@RequestParam("userId") Long userId, @RequestParam("terminalType") Integer terminalType);

}
