package cn.ijiami.detection.android.client.dto;

import com.github.pagehelper.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName TaskDTO.java
 * @Description 检测任务DTO
 * @createTime 2025年01月15日 10:30:00
 */
@Data
@ApiModel(value = "TaskDTO", description = "检测任务DTO")
public class TaskDTO implements Serializable {

    private static final long serialVersionUID = 3838557980677916466L;

    @ApiModelProperty(value = "深度检测数量")
    private Integer deepDetectionCount;

    @ApiModelProperty(value = "快速检测数量")
    private Integer fastDetectionCount;

    @ApiModelProperty(value = "AI检测数量")
    private Integer aiDetectionCount;

    @ApiModelProperty(value = "任务列表")
    private PageInfo<TaskDetailDTO> pageInfo = new PageInfo<>();
}
