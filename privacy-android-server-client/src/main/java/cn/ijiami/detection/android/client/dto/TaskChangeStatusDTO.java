package cn.ijiami.detection.android.client.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName TaskChangeStatusDTO.java
 * @Description 任务状态修改DTO
 * @createTime 2025年01月15日 10:30:00
 */
@Data
@ApiModel("任务状态修改")
@JsonIgnoreProperties(ignoreUnknown = true)
public class TaskChangeStatusDTO implements Serializable {

    private static final long serialVersionUID = -5842078960152819676L;

    @ApiModelProperty(value = "任务id")
    private Long taskId;

    @ApiModelProperty(value = "任务类型 1:动态/深度检测  2:法规检测 3: 复核检测")
    private Integer type;

    @ApiModelProperty(value = "状态 true启动 false中断")
    private Boolean status;

    @ApiModelProperty(value = "设备序列号")
    private String deviceSerial;
    
    @ApiModelProperty(value = "用户ID")
    private Long userId;
}
