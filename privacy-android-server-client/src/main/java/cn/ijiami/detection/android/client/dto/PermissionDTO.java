package cn.ijiami.detection.android.client.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName PermissionDTO.java
 * @Description 权限DTO
 * @createTime 2025年01月15日 10:30:00
 */
@Data
@ApiModel("权限DTO")
public class PermissionDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "权限ID")
    private Long id;

    @ApiModelProperty(value = "权限名称")
    private String permissionName;

    @ApiModelProperty(value = "权限别名")
    private String permissionAlias;

    @ApiModelProperty(value = "权限描述")
    private String permissionDescription;

    @ApiModelProperty(value = "是否敏感")
    private Boolean sensitive;

    @ApiModelProperty(value = "终端类型")
    private Integer terminalType;

    @ApiModelProperty(value = "权限级别")
    private String permissionLevel;

    @ApiModelProperty(value = "权限组")
    private String permissionGroup;
}
