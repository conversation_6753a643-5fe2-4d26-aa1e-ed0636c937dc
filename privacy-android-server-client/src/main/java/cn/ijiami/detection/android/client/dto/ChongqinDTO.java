package cn.ijiami.detection.android.client.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ChongqinDTO.java
 * @Description 重庆DTO
 * @createTime 2025年01月15日 10:30:00
 */
@Data
@ApiModel("重庆DTO")
public class ChongqinDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "真实姓名")
    private String realName;

    @ApiModelProperty(value = "文档ID")
    private String documentId;

    @ApiModelProperty(value = "URL地址")
    private String url;
}
