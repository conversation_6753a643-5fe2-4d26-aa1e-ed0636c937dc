package cn.ijiami.detection.android.client.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName SdkResponseDTO.java
 * @Description 第三方SDK信息响应DTO
 * @createTime 2025年01月15日 10:30:00
 */
@Data
@ApiModel("第三方SDK信息响应DTO")
public class SdkResponseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "三方SDK列表")
    private List<SdkDTO> sdkList;

    @ApiModelProperty(value = "ios隐私SDK信息-未声明SDK")
    private List<SdkDTO> undeclaredList;
}
